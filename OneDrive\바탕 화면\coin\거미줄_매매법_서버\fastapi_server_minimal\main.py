import logging
import os
import sqlite3
import json
import requests
import time
from fastapi import FastAPI, HTTPException
from pydantic import BaseModel
from typing import List, Optional
from typing import Dict
from redis_manager import get_user_status, get_price
from contextlib import asynccontextmanager
from fastapi import APIRouter, HTTPException
from binance.client import Client
from binance.exceptions import BinanceAPIException
import logging
import subprocess
import redis
import asyncio
from fastapi import FastAPI, HTTPException
import logging
import json
from pathlib import Path
from fastapi.staticfiles import StaticFiles
r = redis.Redis(host='localhost', port=6379, db=1, decode_responses=True)

os.makedirs("logs", exist_ok=True)

logging.basicConfig(
    filename="logs/server.log",
    level=logging.INFO,
    format="%(asctime)s [%(levelname)s] %(message)s"
)

errors = {}
DB_PATH = "user_settings.db"
stop_after_profit_flags = {}

def cleanup_error_history():
    """서버 시작 시 기존 오류 데이터 정리 및 구조 마이그레이션"""
    MAX_ERROR_HISTORY = 100
    cleaned_count = 0
    migrated_count = 0

    for username in list(errors.keys()):
        for symbol in list(errors[username].keys()):
            error_data = errors[username][symbol]

            # 구조 마이그레이션 확인
            if "history" not in error_data:
                # 기존 단일 오류를 히스토리로 변환
                old_message = error_data.get("message", "알 수 없는 오류")
                old_timestamp = error_data.get("timestamp", time.time())
                old_stack_trace = error_data.get("stack_trace", "")
                old_count = error_data.get("count", 1)

                errors[username][symbol] = {
                    "message": old_message,
                    "timestamp": old_timestamp,
                    "stack_trace": old_stack_trace,
                    "count": old_count,
                    "history": [
                        {
                            "message": old_message,
                            "timestamp": old_timestamp,
                            "stack_trace": old_stack_trace
                        }
                    ]
                }
                migrated_count += 1

            # 히스토리가 100개를 넘으면 완전 초기화
            history = error_data.get("history", [])
            if len(history) > MAX_ERROR_HISTORY:
                errors[username][symbol]["history"] = []
                errors[username][symbol]["count"] = 0
                errors[username][symbol]["message"] = "없음"
                cleaned_count += len(history)
                logging.info(f"[{username}] {symbol} 오류 히스토리 초기화됨 (100개 초과)")

    if migrated_count > 0 or cleaned_count > 0:
        logging.info(f"오류 데이터 정리 완료: {migrated_count}개 마이그레이션, {cleaned_count}개 히스토리 초기화")

# 서버 시작 시 오류 데이터 정리
cleanup_error_history()

ws_process = None  # 전역 선언

@asynccontextmanager
async def lifespan(app: FastAPI):
    try:
        os.makedirs("logs", exist_ok=True)
        os.makedirs("pids", exist_ok=True)

        # 이전 웹소켓 프로세스 종료
        pid_path = "pids/ws.pid"
        if os.path.exists(pid_path):
            with open(pid_path) as f:
                old_pid = int(f.read())
                try:
                    os.kill(old_pid, 9)
                    logging.info(f"이전 WebSocket 종료됨: {old_pid}")
                except ProcessLookupError:
                    logging.info("이전 WebSocket PID는 이미 종료됨")

        # 새 websocket_manager 실행 (nohup)
        with open("logs/ws.out", "a") as out, open("logs/ws.err", "a") as err:
            proc = subprocess.Popen(
                ["python", "websocket_manager.py"],
                stdout=out,
                stderr=err
            )
        with open(pid_path, "w") as f:
            f.write(str(proc.pid))
        logging.info(f"새 WebSocket 매니저 시작됨 (pid={proc.pid})")

    except Exception as e:
        logging.error(f"WebSocket 매니저 실행 실패: {e}")

    yield

app = FastAPI(
    title="Trading API",
    docs_url=None,
    redoc_url=None,
    openapi_url=None,
    lifespan=lifespan
)
app.mount("/downloads", StaticFiles(directory="downloads"), name="downloads")
def init_db():
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()

    # 기존 사용자 수 확인 (데이터 보호)
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='users'")
    table_exists = cursor.fetchone()

    existing_users = 0
    if table_exists:
        cursor.execute("SELECT COUNT(*) FROM users")
        existing_users = cursor.fetchone()[0]
        print(f"🔒 기존 사용자 {existing_users}명 확인됨")

    # 테이블 생성 (없을 경우만)
    cursor.execute("""
    CREATE TABLE IF NOT EXISTS users (
        username TEXT PRIMARY KEY,
        password TEXT,
        api_key TEXT,
        secret_key TEXT,
        role TEXT
    )
    """)

    # ✅ is_active 컬럼 존재하지 않으면 추가
    cursor.execute("PRAGMA table_info(users)")
    columns = [col[1] for col in cursor.fetchall()]
    if "is_active" not in columns:
        cursor.execute("ALTER TABLE users ADD COLUMN is_active INTEGER DEFAULT 1")
        print("✅ 'is_active' 컬럼이 추가되었습니다.")

    # 데이터 보호 확인
    cursor.execute("SELECT COUNT(*) FROM users")
    final_users = cursor.fetchone()[0]
    if existing_users > 0 and final_users == 0:
        print("🚨 경고: 사용자 데이터가 손실되었습니다!")
        logging.error("사용자 데이터 손실 감지!")

    conn.commit()
    conn.close()

init_db()
API_SECRET_KEY = "tjdwn123!@!@"

class APIKeyRequest(BaseModel):
    api_key: str
    secret_key: str

def is_api_key_registered(api_key: str) -> bool:
    conn = sqlite3.connect(DB_PATH)  # ✅ 올바른 DB 경로 사용
    cursor = conn.cursor()
    try:
        cursor.execute("SELECT 1 FROM users WHERE api_key = ?", (api_key,))
        return cursor.fetchone()
    finally:
        conn.close()

@app.post("/verify_api_key")
async def verify_api_key(data: APIKeyRequest):
    # 1. 이미 등록된 API 키인지 검사
    if is_api_key_registered(data.api_key.strip()):
        raise HTTPException(status_code=409, detail="이미 등록된 API 키입니다.")

    try:
        # 2. 바이낸스 API 인증 시도 (서버 IP 화이트리스트 필요)
        client = Client(data.api_key, data.secret_key)
        client.futures_account()
        return {"success": True}
    except BinanceAPIException as e:
        return {"success": False, "message": f"Binance 오류: {str(e)}"}
    except Exception as e:
        return {"success": False, "message": f"예외 발생: {str(e)}"}


class AdminUser(BaseModel):
    username: str
    password: str
    api_key: str
    secret_key: str
    role: str

@app.post("/admin/register")
def register_user(user: AdminUser):
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()
    try:
        cursor.execute("""
            INSERT OR REPLACE INTO users (username, password, api_key, secret_key, role)
            VALUES (?, ?, ?, ?, ?)
        """, (
            user.username.strip(),
            user.password.strip(),
            user.api_key.strip(),
            user.secret_key.strip(),
            user.role.strip()
        ))
        conn.commit()
    except Exception as e:
        conn.rollback()
        raise HTTPException(status_code=400, detail=str(e))
    finally:
        conn.close()
    # Ensure settings directory exists
    os.makedirs("settings", exist_ok=True)
    return {"message": "등록 완료"}

@app.get("/admin/user/{username}", response_model=AdminUser)
def get_user(username: str):
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()
    cursor.execute(
        "SELECT username, password, api_key, secret_key, role FROM users WHERE username = ?",
        (username.strip(),)
    )
    row = cursor.fetchone()
    conn.close()
    if not row:
        raise HTTPException(status_code=404, detail="사용자를 찾을 수 없습니다.")
    return AdminUser(
        username=row[0], password=row[1],
        api_key=row[2], secret_key=row[3],
        role=row[4]
    )

@app.get("/admin/users", response_model=List[AdminUser])
def get_all_users():
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()
    cursor.execute("SELECT username, password, api_key, secret_key, role FROM users")
    rows = cursor.fetchall()
    conn.close()
    return [
        AdminUser(
            username=row[0], password=row[1],
            api_key=row[2], secret_key=row[3],
            role=row[4]
        ) for row in rows
    ]

class UpdatePassword(BaseModel):
    username: str
    password: str

@app.put("/admin/user/password")
def update_password(data: UpdatePassword):
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()
    cursor.execute(
        "UPDATE users SET password = ? WHERE username = ?",
        (data.password.strip(), data.username.strip())
    )
    conn.commit()
    conn.close()
    return {"message": "비밀번호 업데이트 완료"}

# ✅ 사용자용 비밀번호 변경 API
class ChangePassword(BaseModel):
    username: str
    old_password: str
    new_password: str

@app.put("/user/password")
def change_user_password(data: ChangePassword):
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()

    # 현재 비밀번호 확인
    cursor.execute(
        "SELECT password FROM users WHERE username = ?",
        (data.username.strip(),)
    )
    row = cursor.fetchone()

    if not row:
        conn.close()
        raise HTTPException(status_code=404, detail="사용자를 찾을 수 없습니다.")

    if row[0] != data.old_password.strip():
        conn.close()
        raise HTTPException(status_code=400, detail="현재 비밀번호가 일치하지 않습니다.")

    # 새 비밀번호로 업데이트
    cursor.execute(
        "UPDATE users SET password = ? WHERE username = ?",
        (data.new_password.strip(), data.username.strip())
    )
    conn.commit()
    conn.close()
    return {"message": "비밀번호가 성공적으로 변경되었습니다."}

class UpdateRole(BaseModel):
    username: str
    role: str

@app.put("/admin/user/role")
def update_role(data: UpdateRole):
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()
    cursor.execute(
        "UPDATE users SET role = ? WHERE username = ?",
        (data.role.strip(), data.username.strip())
    )
    conn.commit()
    conn.close()
    return {"message": "역할 업데이트 완료"}

class UpdateUserStatus(BaseModel):
    username: str

@app.put("/admin/user/disable")
def disable_user(data: UpdateUserStatus):
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()
    cursor.execute(
        "UPDATE users SET is_active = 0 WHERE username = ?",
        (data.username.strip(),)
    )
    conn.commit()
    conn.close()
    return {"message": "사용자 비활성화 완료"}

@app.put("/admin/user/enable")
def enable_user(data: UpdateUserStatus):
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()
    cursor.execute(
        "UPDATE users SET is_active = 1 WHERE username = ?",
        (data.username.strip(),)
    )
    conn.commit()
    conn.close()
    return {"message": "사용자 활성화 완료"}

@app.delete("/admin/user/{username}")
def delete_user(username: str):
    username = username.strip()

    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()
    cursor.execute("DELETE FROM users WHERE username = ?", (username,))
    conn.commit()
    conn.close()

    # Delete settings file
    settings_path = os.path.join("settings", f"{username}.json")
    if os.path.exists(settings_path):
        os.remove(settings_path)

    # ✅ Delete user-specific log file
    log_path = os.path.join("logs", f"{username}.log")
    if os.path.exists(log_path):
        os.remove(log_path)

    return {"message": "사용자 삭제 및 관련 파일 정리 완료"}

# --- Settings file-based endpoints ---

class CoinSetting(BaseModel):
    symbol: str
    fraction: float
    mode: str
    leverage: int
    custom_levels: List[float]
    custom_multipliers: List[float]
    poll_interval: float
    take_profit_pct: Optional[float] = None
    entry_threshold_pct: Optional[float] = 0.0
    grid_gap_pct: Optional[float] = None
    stop_loss_pct: Optional[float] = None
    margin_type: Optional[str] = "CROSS"

    # 순환매매용 필드
    cycle_layer_count: Optional[int] = None
    cycle_bottom_layer_pct: Optional[float] = None
    cycle_partial_tp_pct: Optional[float] = None
    cycle_final_tp_pct: Optional[float] = None

class StrategyBlock(BaseModel):
    coins: List[CoinSetting]

class SettingsModel(BaseModel):
    username: str
    grid: StrategyBlock
    cycle: StrategyBlock
    strategy_type: str

@app.get("/settings/{username}")
def get_settings(username: str):
    file_path = os.path.join("settings", f"{username.strip()}.json")
    if not os.path.exists(file_path):
        raise HTTPException(status_code=404, detail="Settings not found")
    try:
        with open(file_path, 'r') as f:
            return json.load(f)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to read settings: {e}")

@app.get("/version")
def get_version():
    try:
        with open("downloads/version.json", "r", encoding="utf-8") as f:
            return json.load(f)
    except FileNotFoundError:
        logging.exception("❌ version.json 파일이 없습니다.")
        raise HTTPException(status_code=404, detail="version.json not found")
    except Exception as e:
        logging.exception(f"❌ version.json 읽기 실패: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to read version.json: {e}")


@app.get("/notice")
def get_notice():
    try:
        path = Path(__file__).parent / "notice.json"
        with path.open("r", encoding="utf-8") as f:
            data = json.load(f)
        return {"notice": data.get("notice", "📭 공지사항이 없습니다.")}
    except FileNotFoundError:
        logging.exception("❌ notice.json 파일을 찾을 수 없습니다.")
        raise HTTPException(status_code=404, detail="Notice file not found")
    except Exception as e:
        logging.exception(f"❌ 알 수 없는 예외 발생: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to read notice: {e}")

@app.post("/settings")
def save_settings(settings: SettingsModel):
    os.makedirs("settings", exist_ok=True)
    file_path = os.path.join("settings", f"{settings.username.strip()}.json")
    try:
        # Write full settings structure
        with open(file_path, "w", encoding="utf-8") as fp:
            json.dump(settings.model_dump(), fp, indent=4, ensure_ascii=False)
        return {"message": "Settings saved"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to save settings: {e}")

@app.get("/log/{username}")
def get_user_log(username: str):
    log_path = os.path.join("logs", f"{username}.log")
    if not os.path.exists(log_path):
        raise HTTPException(status_code=404, detail="로그 파일이 존재하지 않습니다.")
    try:
        with open(log_path, "r", encoding="utf-8") as f:
            return {"log": f.read()}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"로그 파일 읽기 실패: {e}")

# --- Strategy control endpoints ---
router = APIRouter()
task_registry = {}
strategy_processes = {}

@router.post("/run_strategy")
async def run_strategy(data: dict):
    username = data.get("username", "").strip()
    coins = data.get("coins", [])

    if not username or not coins:
        raise HTTPException(status_code=400, detail="Username과 coins 정보가 필요합니다.")

    # ✅ 전략 시작 시 오류 메시지 초기화
    if username in errors:
        errors[username] = {}
        logging.info(f"[{username}] 전략 시작으로 인한 오류 메시지 초기화")

    pid_file = f"pids/{username}.pid"
    if os.path.exists(pid_file):
        with open(pid_file) as f:
            pid = int(f.read())
            if subprocess.call(["ps", "-p", str(pid)], stdout=subprocess.DEVNULL) == 0:
                raise HTTPException(status_code=400, detail="이미 실행 중입니다.")

    log_dir = os.path.join("logs", username)
    os.makedirs(log_dir, exist_ok=True)

    try:
        with open(f"{log_dir}/stdout.log", "a") as out, open(f"{log_dir}/stderr.log", "a") as err:
            proc = subprocess.Popen(
                ["python", "run_strategy.py", username],
                stdout=out,
                stderr=err
            )
        with open(pid_file, "w") as f:
            f.write(str(proc.pid))
        logging.info(f"[{username}] 전략 실행됨 (pid={proc.pid})")
        return {"message": f"{username} 전략 실행 시작됨"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"전략 실행 실패: {e}")
    
@app.post("/close_all_positions")
async def close_all_positions(data: dict):
    from binance.client import Client

    username = data.get("username", "").strip()
    if not username:
        raise HTTPException(status_code=400, detail="username이 필요합니다")

    # DB에서 API 키 조회
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()
    cursor.execute("SELECT api_key, secret_key FROM users WHERE username = ?", (username,))
    row = cursor.fetchone()
    conn.close()

    if not row:
        raise HTTPException(status_code=404, detail="사용자 정보를 찾을 수 없습니다")

    api_key, secret_key = row
    client = Client(api_key.strip(), secret_key.strip())

    try:
        status = get_user_status(username)
        positions = status.get("positions", {})

        for symbol, info in positions.items():
            amt = float(info.get("positionAmt", 0))
            if abs(amt) < 0.0001:
                continue  # 포지션 없으면 건너뜀
            side = "SELL" if amt > 0 else "BUY"

            # 주문 취소 후 포지션 종료
            try:
                client.futures_cancel_all_open_orders(symbol=symbol)
            except Exception as e:
                logging.warning(f"[{username}] {symbol} 주문 취소 실패: {e}")

            try:
                client.futures_create_order(
                    symbol=symbol,
                    side=side,
                    type="MARKET",
                    quantity=abs(amt)
                )
                logging.info(f"[{username}] {symbol} 포지션 종료됨 (qty={abs(amt)})")
            except Exception as e:
                logging.warning(f"[{username}] {symbol} 포지션 종료 실패: {e}")

        return {"message": f"{username}의 모든 포지션 종료 시도 완료"}

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"포지션 종료 중 오류: {e}")

@router.post("/stop_strategy")
async def stop_strategy(data: dict):
    username = data.get("username", "").strip()
    close_positions = data.get("close_positions", False)  # ❗ 기본값 False

    pid_file = f"pids/{username}.pid"
    if not os.path.exists(pid_file):
        return {"message": "실행 중인 전략이 없습니다"}

    with open(pid_file) as f:
        pid = int(f.read())

    try:# 항상 프로세스 종료
        os.kill(pid, 9)
        os.remove(pid_file)
        logging.info(f"[{username}] 전략 종료됨")

        # ✅ 전략 종료 시 오류 메시지 초기화
        if username in errors:
            errors[username] = {}
            logging.info(f"[{username}] 전략 종료로 인한 오류 메시지 초기화")
        
        if close_positions:
            try:
                import requests
                res = await asyncio.to_thread(
                    requests.post,
                    "http://localhost:8000/close_all_positions",
                    json={"username": username}
                )
                if res.status_code == 200:
                    logging.info(f"[{username}] 모든 포지션 종료 완료")
                else:
                    logging.warning(f"[{username}] 포지션 종료 실패: {res.text}")
            except Exception as e:
                logging.warning(f"[{username}] 포지션 종료 요청 실패: {e}")
        
        # ✅ Redis 플래그 삭제
        try:
            r = redis.Redis(host='localhost', port=6379, db=1, decode_responses=True)
            r.delete(f"stop_after_profit:{username}")
            logging.info(f"[{username}] stop_after_profit Redis 키 삭제됨")
        except Exception as e:
            logging.warning(f"[{username}] Redis stop_after_profit 키 삭제 실패: {e}")

        return {"message": f"{username} 전략 중지 완료"}
        

    except ProcessLookupError:
        os.remove(pid_file)
        return {"message": "이미 종료된 전략이었습니다"}
    except Exception as e:
        logging.exception(f"[{username}] 전략 종료 중 오류: {e}")
        raise HTTPException(status_code=500, detail="전략 종료 실패")


@app.post("/stop_after_profit")
async def stop_after_profit(data: dict):
    username = data.get("username", "").strip()
    if not username:
        raise HTTPException(status_code=400, detail="Username required")

    try:
        r.set(f"stop_after_profit:{username}", "1")  # 1시간만 유지
        logging.info(f"[{username}] Redis에 stop_after_profit 설정됨")
        return {"message": f"{username} 전략이 익절 후 중지되도록 설정되었습니다."}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Redis 설정 실패: {e}")

@app.get("/monitor/data")
async def get_monitor_data():
    """모니터링용 통합 데이터 API"""
    try:
        result = {
            "users": [],
            "redis_info": {},
            "server_status": "running"
        }

        # 1. 활성 사용자 목록 가져오기
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()
        cursor.execute("SELECT username, is_active FROM users WHERE is_active = 1")
        active_users = cursor.fetchall()
        conn.close()

        # 2. 각 사용자별 데이터 수집
        for username, is_active in active_users:
            user_data = {
                "username": username,
                "is_active": bool(is_active),
                "redis_data": {},
                "tp_records": {},
                "settings": {}
            }

            # Redis 데이터 수집
            try:
                # 사용자 상태
                status_raw = r.get(f"user:{username}:status")
                if status_raw:
                    user_data["redis_data"]["status"] = json.loads(status_raw)

                # 잔고 정보
                balances_raw = r.get(f"user:{username}:balances")
                if balances_raw:
                    user_data["redis_data"]["balances"] = json.loads(balances_raw)

                # 포지션 정보
                positions_raw = r.get(f"user:{username}:positions")
                if positions_raw:
                    user_data["redis_data"]["positions"] = json.loads(positions_raw)

                # 주문 정보
                orders_raw = r.get(f"user:{username}:orders")
                if orders_raw:
                    user_data["redis_data"]["orders"] = json.loads(orders_raw)

            except Exception as e:
                user_data["redis_data"]["error"] = str(e)

            # TP 기록 파일 읽기
            tp_file_path = f"tp_records/{username}_cycle_tp.json"
            if os.path.exists(tp_file_path):
                try:
                    with open(tp_file_path, 'r') as f:
                        tp_data = json.load(f)
                        user_data["tp_records"] = tp_data.get("tp", {})
                except Exception as e:
                    user_data["tp_records"]["error"] = str(e)

            # 설정 파일 읽기
            settings_path = f"settings/{username}.json"
            if os.path.exists(settings_path):
                try:
                    with open(settings_path, 'r') as f:
                        user_data["settings"] = json.load(f)
                except Exception as e:
                    user_data["settings"]["error"] = str(e)

            result["users"].append(user_data)

        # 3. Redis 전체 정보
        try:
            all_keys = r.keys("*")
            user_keys = [k for k in all_keys if k.startswith("user:")]
            price_keys = [k for k in all_keys if k.startswith("price:")]

            result["redis_info"] = {
                "total_keys": len(all_keys),
                "user_keys": len(user_keys),
                "price_keys": len(price_keys),
                "sample_keys": all_keys[:10] if all_keys else []
            }
        except Exception as e:
            result["redis_info"]["error"] = str(e)

        return result

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"모니터링 데이터 수집 실패: {e}")

@app.get("/monitor/prices")
async def get_current_prices():
    """현재 가격 정보 API"""
    try:
        price_keys = r.keys("price:*")
        prices = {}

        for key in price_keys:
            symbol = key.replace("price:", "")
            price_raw = r.get(key)
            if price_raw:
                try:
                    prices[symbol] = float(price_raw)
                except:
                    prices[symbol] = 0.0

        return {"prices": prices}

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"가격 정보 조회 실패: {e}")

@app.post("/symbol_filters")
def save_symbol_filters(filters_data: dict):
    """
    클라이언트에서 전송한 심볼 필터 정보를 코인별 JSON 파일에 저장
    """
    try:
        import json
        import os

        # coin 폴더 생성
        coin_dir = "coin"
        if not os.path.exists(coin_dir):
            os.makedirs(coin_dir)

        saved_count = 0
        for symbol, filter_info in filters_data.items():
            try:
                # 코인별 파일 경로
                coin_file = os.path.join(coin_dir, f"{symbol}.json")

                # 기존 데이터 로드 (있다면)
                existing_data = {}
                if os.path.exists(coin_file):
                    try:
                        with open(coin_file, 'r', encoding='utf-8') as f:
                            existing_data = json.load(f)
                    except:
                        existing_data = {}

                # 필터 정보 업데이트
                existing_data["filters"] = filter_info
                existing_data["last_update"] = int(time.time() * 1000)

                # 파일에 저장
                with open(coin_file, 'w', encoding='utf-8') as f:
                    json.dump(existing_data, f, indent=2, ensure_ascii=False)

                saved_count += 1

            except Exception as e:
                logging.error(f"심볼 {symbol} 필터 저장 실패: {e}")
                continue

        return {"status": "success", "message": f"{saved_count}개 심볼 필터 정보 저장 완료"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"필터 정보 저장 실패: {e}")

@app.post("/candle_data")
def save_candle_data(candle_data: dict):
    """
    클라이언트에서 전송한 캔들 데이터를 코인별 JSON 파일에 저장
    조건부 진입을 위한 기술적 지표 계산용
    """
    try:
        import json
        import os

        # coin 폴더 생성
        coin_dir = "coin"
        if not os.path.exists(coin_dir):
            os.makedirs(coin_dir)

        saved_count = 0
        for symbol, candle_info in candle_data.items():
            try:
                # 코인별 파일 경로
                coin_file = os.path.join(coin_dir, f"{symbol}.json")

                # 기존 데이터 로드 (있다면)
                existing_data = {}
                if os.path.exists(coin_file):
                    try:
                        with open(coin_file, 'r', encoding='utf-8') as f:
                            existing_data = json.load(f)
                    except:
                        existing_data = {}

                # 캔들 데이터 업데이트
                timeframe = candle_info.get("timeframe", "4h")
                if "candle_data" not in existing_data:
                    existing_data["candle_data"] = {}

                existing_data["candle_data"][timeframe] = candle_info
                existing_data["last_update"] = int(time.time() * 1000)

                # 파일에 저장
                with open(coin_file, 'w', encoding='utf-8') as f:
                    json.dump(existing_data, f, indent=2, ensure_ascii=False)

                saved_count += 1

            except Exception as e:
                logging.error(f"심볼 {symbol} 캔들 데이터 저장 실패: {e}")
                continue

        return {"status": "success", "message": f"{saved_count}개 심볼 캔들 데이터 저장 완료"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"캔들 데이터 저장 실패: {e}")

def cleanup_unused_symbols():
    """사용하지 않는 심볼의 데이터 정리"""
    try:
        import json
        import os

        # 현재 사용 중인 심볼들 수집
        used_symbols = set()

        # 모든 사용자의 설정에서 사용 중인 심볼 수집
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()
        cursor.execute("SELECT username, settings FROM user_settings")

        for username, settings_str in cursor.fetchall():
            try:
                settings = json.loads(settings_str)

                # grid 전략 심볼들
                if "grid" in settings:
                    for coin in settings["grid"].get("coins", []):
                        if coin.get("symbol"):
                            used_symbols.add(coin["symbol"])

                # cycle 전략 심볼들
                if "cycle" in settings:
                    for coin in settings["cycle"].get("coins", []):
                        if coin.get("symbol"):
                            used_symbols.add(coin["symbol"])

            except Exception as e:
                logging.error(f"사용자 {username} 설정 파싱 오류: {e}")
                continue

        conn.close()

        # coin 폴더의 사용하지 않는 파일들 정리
        coin_dir = "coin"
        if os.path.exists(coin_dir):
            removed_files = []
            for filename in os.listdir(coin_dir):
                if filename.endswith('.json'):
                    symbol = filename[:-5]  # .json 제거
                    if symbol not in used_symbols:
                        file_path = os.path.join(coin_dir, filename)
                        try:
                            os.remove(file_path)
                            removed_files.append(symbol)
                        except Exception as e:
                            logging.error(f"파일 삭제 실패 {filename}: {e}")

            if removed_files:
                logging.info(f"사용하지 않는 심볼 파일 정리 완료: {len(removed_files)}개 ({', '.join(removed_files)})")

        # 기존 symbol_filters.json 파일도 정리 (호환성 유지)
        filters_file = "symbol_filters.json"
        if os.path.exists(filters_file):
            try:
                os.remove(filters_file)
                logging.info("기존 symbol_filters.json 파일 제거됨")
            except Exception as e:
                logging.error(f"기존 파일 제거 실패: {e}")

    except Exception as e:
        logging.error(f"심볼 데이터 정리 오류: {e}")



@app.get("/price")
def get_public_price(symbol: str):
    """
    Proxy to Binance public REST endpoint for ticker price.
    """
    try:
        resp = requests.get(
            "https://fapi.binance.com/fapi/v1/ticker/price",
            params={"symbol": symbol.upper()},
            timeout=5
        )
        data = resp.json()
        return {"symbol": symbol.upper(), "price": float(data["price"])}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"가격 조회 실패: {e}")

@app.post("/report_error")
def report_error(data: Dict):
    username = data.get("username")
    symbol = data.get("symbol")
    message = data.get("message", "없음")
    timestamp = data.get("timestamp", time.time())
    stack_trace = data.get("stack_trace", "")  # 스택트레이스 추가

    if not username or not symbol:
        raise HTTPException(status_code=400, detail="username과 symbol이 필요합니다")

    if username not in errors:
        errors[username] = {}

    # 오류 초기화 요청인 경우
    if message == "없음":
        errors[username][symbol] = {
            "message": "없음",
            "timestamp": timestamp,
            "stack_trace": "",
            "count": 0,
            "history": []  # 오류 히스토리 초기화
        }
        logging.info(f"[{username}] {symbol} 오류 초기화됨")
        return {"message": "오류 초기화 완료"}

    # 기존 오류 데이터 구조 확인 및 마이그레이션
    if symbol not in errors[username]:
        # 새로운 심볼의 첫 번째 오류
        errors[username][symbol] = {
            "message": message,
            "timestamp": timestamp,
            "stack_trace": stack_trace,
            "count": 1,
            "history": []
        }
    else:
        # 기존 오류 데이터가 있는 경우 구조 확인
        existing_error = errors[username][symbol]

        # 기존 구조가 새로운 구조가 아닌 경우 마이그레이션
        if "history" not in existing_error:
            # 기존 단일 오류를 히스토리로 변환
            old_message = existing_error.get("message", "알 수 없는 오류")
            old_timestamp = existing_error.get("timestamp", time.time())
            old_stack_trace = existing_error.get("stack_trace", "")
            old_count = existing_error.get("count", 1)

            errors[username][symbol] = {
                "message": message,
                "timestamp": timestamp,
                "stack_trace": stack_trace,
                "count": old_count + 1,
                "history": [
                    {
                        "message": old_message,
                        "timestamp": old_timestamp,
                        "stack_trace": old_stack_trace
                    }
                ]
            }
            logging.info(f"[{username}] {symbol} 오류 구조 마이그레이션 완료")
        else:
            # 새로운 구조인 경우 정상 업데이트
            errors[username][symbol]["message"] = message
            errors[username][symbol]["timestamp"] = timestamp
            errors[username][symbol]["stack_trace"] = stack_trace
            errors[username][symbol]["count"] += 1

    # 히스토리가 100개를 넘는지 먼저 체크
    MAX_ERROR_HISTORY = 100
    current_history = errors[username][symbol].get("history", [])

    if len(current_history) >= MAX_ERROR_HISTORY:
        # 100개 이상이면 완전 초기화 후 새 오류 추가
        errors[username][symbol]["history"] = [{
            "message": message,
            "timestamp": timestamp,
            "stack_trace": stack_trace
        }]
        errors[username][symbol]["count"] = 1  # 카운트도 1로 리셋
        errors[username][symbol]["message"] = message
        errors[username][symbol]["timestamp"] = timestamp
        errors[username][symbol]["stack_trace"] = stack_trace
        logging.info(f"[{username}] {symbol} 오류 히스토리 초기화됨 (100개 초과로 인한 자동 초기화)")
    else:
        # 100개 미만이면 정상적으로 추가
        errors[username][symbol]["history"].append({
            "message": message,
            "timestamp": timestamp,
            "stack_trace": stack_trace
        })

    logging.info(f"[{username}] {symbol} 오류 보고됨: {message} (총 {errors[username][symbol]['count']}회, 히스토리 {len(errors[username][symbol]['history'])}개)")
    return {"message": "오류 등록 완료"}

def clear_error_for_symbol(username: str, symbol: str):
    """특정 심볼의 오류만 정리하는 함수 (웹소켓 매니저에서 호출)"""
    try:
        if username in errors and symbol in errors[username]:
            errors[username][symbol] = {
                "message": "없음",
                "timestamp": time.time(),
                "stack_trace": "",
                "count": 0,
                "history": []
            }
            logging.info(f"[{username}] {symbol} 오류 정리 완료")
    except Exception as e:
        logging.error(f"오류 정리 실패 ({username}, {symbol}): {e}")

@app.post("/clear_error")
def clear_error(data: Dict):
    username = data.get("username")

    errors[username] = {}
    logging.info(f"[{username}] 오류가 초기화됨")
    return {"message": "오류 초기화 완료"}

@app.post("/cleanup_all_errors")
def cleanup_all_errors():
    """모든 사용자의 오류 히스토리를 초기화 (100개 초과 시)"""
    MAX_ERROR_HISTORY = 100
    total_cleaned = 0
    total_migrated = 0

    for username in list(errors.keys()):
        for symbol in list(errors[username].keys()):
            error_data = errors[username][symbol]

            # 구조 마이그레이션 확인
            if "history" not in error_data:
                # 기존 단일 오류를 히스토리로 변환
                old_message = error_data.get("message", "알 수 없는 오류")
                old_timestamp = error_data.get("timestamp", time.time())
                old_stack_trace = error_data.get("stack_trace", "")
                old_count = error_data.get("count", 1)

                errors[username][symbol] = {
                    "message": old_message,
                    "timestamp": old_timestamp,
                    "stack_trace": old_stack_trace,
                    "count": old_count,
                    "history": [
                        {
                            "message": old_message,
                            "timestamp": old_timestamp,
                            "stack_trace": old_stack_trace
                        }
                    ]
                }
                total_migrated += 1

            # 히스토리가 100개를 넘으면 완전 초기화
            history = error_data.get("history", [])
            if len(history) > MAX_ERROR_HISTORY:
                errors[username][symbol]["history"] = []
                errors[username][symbol]["count"] = 0
                errors[username][symbol]["message"] = "없음"
                total_cleaned += len(history)
                logging.info(f"[{username}] {symbol} 오류 히스토리 초기화됨 (100개 초과)")

    logging.info(f"전체 오류 데이터 정리 완료: {total_migrated}개 마이그레이션, {total_cleaned}개 히스토리 초기화")
    return {"message": f"오류 정리 완료: {total_migrated}개 마이그레이션, {total_cleaned}개 초기화"}

@app.get("/error_history/{username}/{symbol}")
def get_error_history(username: str, symbol: str, limit: int = 50):
    """특정 심볼의 오류 히스토리 조회"""
    try:
        if username not in errors or symbol not in errors[username]:
            return {"history": [], "total_count": 0}

        error_data = errors[username][symbol]
        history = error_data.get("history", [])

        # 최신 순으로 정렬하여 반환 (최신이 먼저)
        recent_history = sorted(history, key=lambda x: x["timestamp"], reverse=True)

        # limit 적용
        if limit > 0:
            recent_history = recent_history[:limit]

        return {
            "history": recent_history,
            "total_count": len(history),
            "current_error": {
                "message": error_data.get("message", "없음"),
                "count": error_data.get("count", 0),
                "timestamp": error_data.get("timestamp", 0)
            }
        }
    except Exception as e:
        logging.error(f"오류 히스토리 조회 실패 ({username}/{symbol}): {e}")
        raise HTTPException(status_code=500, detail=f"오류 히스토리 조회 실패: {e}")

@app.post("/close_position")
async def close_position(data: dict):
    username = data.get("username", "").strip()
    symbol = data.get("symbol", "").strip().upper()

    if not username or not symbol:
        raise HTTPException(status_code=400, detail="username과 symbol 필요")

    import os
    from binance.client import Client

    settings_path = os.path.join("settings", f"{username}.json")
    if not os.path.exists(settings_path):
        raise HTTPException(status_code=404, detail="설정 파일 없음")

    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()
    cursor.execute("SELECT api_key, secret_key FROM users WHERE username = ?", (username,))
    row = cursor.fetchone()
    conn.close()
    if not row:
        raise HTTPException(status_code=404, detail="API 키 없음")

    api_key, secret_key = row
    client = Client(api_key.strip(), secret_key.strip())

    try:
        client.futures_cancel_all_open_orders(symbol=symbol)
        status = get_user_status(username)
        positions = status.get("positions", {})
        amt = float(positions.get(symbol, {}).get("positionAmt", 0))
        if abs(amt) > 0.0001:
            side = "SELL" if amt > 0 else "BUY"
            client.futures_create_order(symbol=symbol, side=side, type="MARKET", quantity=abs(amt))
        return {"message": f"{symbol} 포지션 종료 완료"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"{symbol} 종료 실패: {e}")
    
@app.post("/clear_log")
def clear_user_log(data: dict):
    username = data.get("username", "").strip()
    if not username:
        raise HTTPException(status_code=400, detail="username이 필요합니다.")

    log_path = os.path.join("logs", f"{username}.log")
    if not os.path.exists(log_path):
        raise HTTPException(status_code=404, detail="로그 파일이 존재하지 않습니다.")

    try:
        open(log_path, "w").close()  # 내용 비우기
        return {"message": "로그 초기화 완료"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"로그 초기화 실패: {e}")
    
def is_strategy_running(username: str) -> str:
    pid_file = f"pids/{username}.pid"
    if not os.path.exists(pid_file):
        return "not_running"

    try:
        with open(pid_file) as f:
            pid = int(f.read())

        # 해당 PID가 살아 있는지 확인
        os.kill(pid, 0)  # 예외 없으면 살아 있음

        # ✅ Redis에 stop_after_profit 키가 존재하는지 확인
        if r.get(f"stop_after_profit:{username}"):
            return "stop_after_profit"

        return "running"
    except ProcessLookupError:
        os.remove(pid_file)
        return "not_running"
    except Exception as e:
        print(f"전략 실행 상태 확인 중 오류: {e}")
        return "not_running"

@app.get("/strategy/running_status/{username}")
def get_strategy_running_status(username: str):
    """전략 실행 상태 확인 API"""
    try:
        status = is_strategy_running(username)

        status_map = {
            "running": {"running": True, "status": "running", "message": "전략 실행 중"},
            "stop_after_profit": {"running": True, "status": "stop_after_profit", "message": "익절 후 종료 대기 중"},
            "not_running": {"running": False, "status": "not_running", "message": "전략 중지됨"}
        }

        return status_map.get(status, {"running": False, "status": "unknown", "message": "상태 불명"})
    except Exception as e:
        logging.error(f"전략 상태 확인 실패 ({username}): {e}")
        return {"running": False, "status": "error", "message": f"오류: {e}"}

@app.get("/position_analysis/{username}")
def get_position_analysis(username: str):
    """포지션 분석 데이터 조회 (websocket_manager에서 생성된 데이터)"""
    try:
        # Redis에서 포지션 분석 데이터 가져오기
        analysis_key = f"position_analysis:{username}"
        data_str = r.get(analysis_key)

        if data_str:
            analysis_data = json.loads(data_str)
            return {"analysis_data": analysis_data}
        else:
            return {"analysis_data": {}}

    except Exception as e:
        logging.error(f"포지션 분석 데이터 조회 실패: {e}")
        return {"analysis_data": {}}

@app.get("/position_pnl/{username}")
def get_position_pnl(username: str):
    """포지션별 실현손익 정보 조회"""
    try:
        analysis_key = f"position_analysis:{username}"
        data_str = r.get(analysis_key)

        if data_str:
            analysis_data = json.loads(data_str)
            pnl_summary = {}

            for symbol, data in analysis_data.items():
                pnl_summary[symbol] = {
                    "realized_pnl": data.get("realized_pnl", 0.0),
                    "total_commission": data.get("total_commission", 0.0),
                    "trade_count": data.get("analysis_count", 0),
                    "position_side": data.get("position_side", "UNKNOWN"),
                    "entry_price": data.get("entry_price", 0.0),
                    "last_updated": data.get("last_updated", 0)
                }

            return {"pnl_data": pnl_summary}

        return {"pnl_data": {}}
    except Exception as e:
        logging.error(f"실현손익 데이터 조회 실패: {e}")
        return {"pnl_data": {}, "error": str(e)}

@app.get("/limit_orders/{username}")
def get_limit_orders(username: str):
    """사용자의 현재 지정가 주문 조회"""
    try:
        # Redis에서 사용자 정보 조회
        user_data_str = r.get(f"user_data:{username}")
        if not user_data_str:
            return {"orders": {}, "error": "사용자 데이터 없음"}

        user_data = json.loads(user_data_str)
        api_key = user_data.get("api_key")
        secret_key = user_data.get("secret_key")

        if not api_key or not secret_key:
            return {"orders": {}, "error": "API 키 정보 없음"}

        # 바이낸스 클라이언트 생성
        from binance.client import Client
        client = Client(api_key, secret_key)

        # 현재 지정가 주문 조회
        open_orders = client.futures_get_open_orders()

        # 심볼별로 그룹화
        orders_by_symbol = {}
        for order in open_orders:
            symbol = order['symbol']
            if symbol not in orders_by_symbol:
                orders_by_symbol[symbol] = []

            orders_by_symbol[symbol].append({
                'orderId': order['orderId'],
                'symbol': order['symbol'],
                'price': order['price'],
                'origQty': order['origQty'],
                'executedQty': order['executedQty'],
                'status': order['status'],
                'type': order['type'],
                'side': order['side'],
                'time': order['time']
            })

        return {"orders": orders_by_symbol}

    except Exception as e:
        logging.error(f"지정가 주문 조회 실패 ({username}): {e}")
        return {"orders": {}, "error": str(e)}

@app.get("/strategy_status/{username}")
async def get_strategy_status(username: str):
    from binance.client import Client
    import os

    # DB에서 API 키 조회
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()
    cursor.execute("SELECT api_key, secret_key FROM users WHERE username = ?", (username,))
    row = cursor.fetchone()
    conn.close()

    if not row:
        raise HTTPException(status_code=404, detail="사용자 없음")
    # 잔고 조회
    try:
        status = get_user_status(username)
        balances_raw = status.get("balances", {})

        if isinstance(balances_raw, list):
            logging.info(f"[{username}] balances가 리스트 형식으로 반환됨")
            # 리스트면 dict로 변환
            balances = {b["asset"]: b for b in balances_raw if "asset" in b}
        elif isinstance(balances_raw, dict):
            balances = balances_raw
        else:
            balances = {}

        wallet_balance = float(balances.get("USDT", {}).get("walletBalance", 0))

    except Exception as e:
        logging.info(f"[{username}] 잔고 조회 실패: {e}")
        logging.info(f"[stsatus] {status}")
        wallet_balance = 0

    try:
        status = get_user_status(username)
        positions = status.get("positions", {})
        total_unrealized_profit = 0.0

        for sym, data in positions.items():
            amt = float(data.get("positionAmt", 0))
            entry = float(data.get("entryPrice", 0))
            if amt == 0 or entry == 0:
                continue
            try:
                mark = await get_price(sym)
                if mark is None or mark <= 0:
                    continue
                # ✅ 안전한 PNL 계산 (None 값 방지, 숏 포지션 고려)
                if entry is None or entry <= 0:
                    continue
                # 올바른 PNL 계산: 롱은 (현재가-진입가)*수량, 숏은 (진입가-현재가)*수량
                if amt > 0:  # 롱 포지션
                    pnl = (mark - entry) * amt
                else:  # 숏 포지션
                    pnl = (entry - mark) * abs(amt)
                total_unrealized_profit += pnl
            except Exception as e:
                logging.info(f"[{username}] {sym} 가격 조회 실패 (PNL 계산 생략): {e}")
    except Exception as e:
        logging.info(f"[{username}] 미실현 손익 계산 실패: {e}")
        total_unrealized_profit = 0.0

    total_usdt = wallet_balance + total_unrealized_profit

    # 설정 로드
    settings_path = os.path.join("settings", f"{username}.json")
    if not os.path.exists(settings_path):
        raise HTTPException(status_code=404, detail="설정 파일 없음")
    with open(settings_path) as f:
        config = json.load(f)

    # 전략 종류 (예: grid, cycle)
    strategy_type = config.get("strategy_type", "알수없음")
    strategy_config = config.get(strategy_type, {})
    strategy_symbols = [coin["symbol"] for coin in strategy_config.get("coins", [])]

    status = get_user_status(username)
    positions = status.get("positions", {})
    try:
        # 포지션이 있는 코인들
        position_coins = [sym for sym, data in positions.items() if abs(float(data.get("positionAmt", 0))) > 0]

        # 오류가 있는 코인들 (오류 메시지가 "없음"이 아닌 경우)
        error_coins = []
        user_errors = errors.get(username, {})
        for sym, error_info in user_errors.items():
            if isinstance(error_info, dict):
                if error_info.get("message", "없음") != "없음":
                    error_coins.append(sym)
            elif error_info != "없음":
                error_coins.append(sym)

        # 포지션이 있거나 오류가 있는 모든 코인들
        coins = list(set(position_coins + error_coins))
    except Exception as e:
        logging.info(f"[{username}] 포지션 조회 실패: {e}")
        coins = []
    status_str = is_strategy_running(username)

    result = {
        "username": username,
        "wallet_balance": wallet_balance,
        "total_usdt": total_usdt,
        "unrealized_pnl": total_unrealized_profit,
        "is_running": status_str,
        "strategy_type": strategy_type,
        "symbols": strategy_symbols,
        "positions": [],
        "errors": errors.get(username, {})
    }

    for coin in coins:
        sym = coin.upper()
        try:
            status = get_user_status(username)
            positions = status.get("positions", {})
            amt = float(positions.get(sym, {}).get("positionAmt", 0))
            entry = float(positions.get(sym, {}).get("entryPrice", 0))
            mark = await get_price(sym)
            # ✅ 안전한 PNL 및 ROI 계산 (None 값 방지)
            if mark is None or mark <= 0 or entry is None or entry <= 0:
                pnl = 0
                ROI = 0
            else:
                # ✅ 올바른 PNL 및 ROI 계산 (숏 포지션 고려)
                if amt > 0:  # 롱 포지션
                    pnl = (mark - entry) * amt
                    ROI = (mark - entry) / entry * 100
                else:  # 숏 포지션
                    pnl = (entry - mark) * abs(amt)
                    ROI = (entry - mark) / entry * 100

            status = get_user_status(username)
            open_orders_dict = status.get("open_limit_orders", {})
            order_count = open_orders_dict.get(sym, 0)

            # 포지션이 없어도 오류가 있으면 표시
            if amt == 0:
                # 오류가 있는지 확인
                user_errors = errors.get(username, {})
                error_info = user_errors.get(sym, {})
                has_error = False

                if isinstance(error_info, dict):
                    has_error = error_info.get("message", "없음") != "없음"
                elif error_info != "없음":
                    has_error = True

                if not has_error:
                    continue

                # 오류만 있고 포지션이 없는 경우
                result["positions"].append({
                    "symbol": sym,
                    "side": "오류",
                    "amount": 0,
                    "entry_price": 0,
                    "mark_price": mark if mark else 0,
                    "pnl": 0,
                    "ROI": 0,
                    "open_orders": order_count
                })
            else:
                # 포지션이 있는 경우
                result["positions"].append({
                    "symbol": sym,
                    "side": "Long" if amt > 0 else "Short" if amt < 0 else "없음",
                    "amount": abs(amt),
                    "entry_price": entry,
                    "mark_price": mark,
                    "pnl": round(pnl, 2),
                    "ROI": round(ROI, 2),
                    "open_orders": order_count
                })
        except Exception as e:
            result["errors"][sym] = f"주문 조회 실패: {e}"

    return result

@app.get("/strategy_info/{username}")
def get_strategy_info(username: str):
    """CoinPilot용 전략 정보 API - Redis 데이터 직접 반환"""
    try:
        # Redis에서 사용자 상태 데이터 직접 조회
        status = get_user_status(username)

        if not status:
            return {
                "balances": {},
                "positions": {},
                "orders": [],
                "open_limit_orders": {},
                "total_usdt": 0
            }

        return status

    except Exception as e:
        logging.error(f"strategy_info 조회 실패 ({username}): {e}")
        return {
            "balances": {},
            "positions": {},
            "orders": [],
            "open_limit_orders": {},
            "total_usdt": 0,
            "error": str(e)
        }

@app.post("/force_clear_positions/{username}")
def force_clear_positions(username: str):
    """포지션 데이터 강제 정리 API"""
    try:
        # Redis에서 현재 상태 조회
        status = get_user_status(username)

        if status:
            # 포지션 데이터만 빈 딕셔너리로 설정
            status["positions"] = {}

            # Redis에 업데이트
            from redis_manager import set_user_status
            set_user_status(username, status)

            logging.info(f"[{username}] 포지션 데이터 강제 정리 완료")
            return {"success": True, "message": "포지션 데이터가 정리되었습니다."}
        else:
            return {"success": False, "message": "사용자 데이터를 찾을 수 없습니다."}

    except Exception as e:
        logging.error(f"포지션 강제 정리 실패 ({username}): {e}")
        return {"success": False, "message": f"오류: {e}"}

app.include_router(router)