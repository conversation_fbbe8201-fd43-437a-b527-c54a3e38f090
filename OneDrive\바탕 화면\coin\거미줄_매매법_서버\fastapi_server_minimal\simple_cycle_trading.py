#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
단일 사용자용 순환매매 시스템
- 웹소켓 없이 바이낸스 API만 사용
- 실시간 모니터링 및 자동 거래
- 올인원 파일 (설정 포함)

사용법:
1. 아래 설정 부분에서 API_KEY와 SECRET_KEY를 입력
2. COINS_CONFIG에서 거래할 코인과 설정 조정
3. python simple_cycle_trading.py 실행
4. Ctrl+C로 종료

전략 모드:
- "conditional": 조건부 진입 (RSI/Stoch RSI 지표 기반 롱/숏 자동 선택)
- "long": 롱만
- "short": 숏만
"""

import json
import logging
import asyncio
import signal
import sys
import websockets
import threading
from datetime import datetime
from binance.client import Client

# ==================== 설정 부분 ====================
# 여기서 설정을 직접 수정하세요

# 바이낸스 API 설정
API_KEY = "YOUR_BINANCE_API_KEY_HERE"
SECRET_KEY = "YOUR_BINANCE_SECRET_KEY_HERE"

# 거래할 코인 설정
COINS_CONFIG = [
    {
        "symbol": "ADAUSDT",
        "mode": "conditional",  # "conditional", "long", "short"
        "layer_count": 5,       # 레이어 개수
        "bottom_pct": 50.0,     # 최하단/최상단 레이어 퍼센트
        "fraction": 0.1,        # 투자 비율 (전체 잔고 대비)
        "leverage": 1,          # 레버리지
        "partial_tp_pct": 2.0,  # 부분 익절 퍼센트
        "final_tp_pct": 5.0,    # 최종 익절 퍼센트
        "stop_loss_pct": 10.0   # 손절 퍼센트
    },
    {
        "symbol": "ONTUSDT",
        "mode": "conditional",
        "layer_count": 5,
        "bottom_pct": 50.0,
        "fraction": 0.1,
        "leverage": 1,
        "partial_tp_pct": 2.0,
        "final_tp_pct": 5.0,
        "stop_loss_pct": 10.0
    }
]

# ==================== 설정 끝 ====================

# 로깅 설정
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] %(message)s',
    handlers=[
        logging.FileHandler('cycle_trading.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class SimpleCycleTrading:
    def __init__(self):
        """초기화"""
        self.client = None
        self.running = False
        self.positions = {}  # 포지션 추적
        self.orders = {}     # 주문 추적
        self.layer_file = "layer_tracking.json"  # 레이어 추적 파일
        self.layer_data = {}  # 레이어 추적 데이터

        # 웹소켓 관련
        self.prices = {}  # 실시간 가격 저장
        self.websocket_running = False
        self.listen_key = None
        self.price_ws_task = None
        self.user_ws_task = None

        # 레이어 추적 데이터 로드
        self.load_layer_data()

        # 바이낸스 클라이언트 초기화
        self.init_binance_client()

    def init_binance_client(self):
        """바이낸스 클라이언트 초기화"""
        try:
            if not API_KEY or not SECRET_KEY or API_KEY == "YOUR_BINANCE_API_KEY_HERE":
                logger.error("API 키 또는 시크릿 키가 설정되지 않았습니다")
                logger.error("파일 상단의 API_KEY와 SECRET_KEY를 설정해주세요")
                return False

            self.client = Client(API_KEY, SECRET_KEY)

            # 연결 테스트
            self.client.futures_account()
            logger.info("바이낸스 API 연결 성공")
            return True

        except Exception as e:
            logger.error(f"바이낸스 API 연결 실패: {e}")
            return False

    async def start_websockets(self):
        """웹소켓 시작"""
        try:
            # Listen Key 생성 (사용자 데이터 스트림용)
            self.listen_key = self.client.futures_stream_get_listen_key()['listenKey']

            # 가격 웹소켓 시작
            symbols = [coin['symbol'].lower() for coin in COINS_CONFIG]
            self.price_ws_task = asyncio.create_task(self.price_websocket(symbols))

            # 사용자 데이터 웹소켓 시작
            self.user_ws_task = asyncio.create_task(self.user_data_websocket())

            self.websocket_running = True
            logger.info("웹소켓 연결 완료")

        except Exception as e:
            logger.error(f"웹소켓 시작 실패: {e}")

    async def price_websocket(self, symbols):
        """가격 웹소켓"""
        try:
            # 심볼들의 ticker 스트림 구독
            streams = [f"{symbol}@ticker" for symbol in symbols]
            url = f"wss://fstream.binance.com/stream?streams={'/'.join(streams)}"

            async with websockets.connect(url) as websocket:
                logger.info(f"가격 웹소켓 연결: {symbols}")

                while self.websocket_running:
                    try:
                        message = await asyncio.wait_for(websocket.recv(), timeout=30)
                        data = json.loads(message)

                        if 'data' in data:
                            stream_data = data['data']
                            symbol = stream_data['s']
                            price = float(stream_data['c'])  # 현재가

                            self.prices[symbol] = price

                    except asyncio.TimeoutError:
                        # 30초마다 ping 전송
                        await websocket.ping()
                    except Exception as e:
                        logger.error(f"가격 웹소켓 오류: {e}")
                        break

        except Exception as e:
            logger.error(f"가격 웹소켓 연결 실패: {e}")

    async def user_data_websocket(self):
        """사용자 데이터 웹소켓 (주문 체결 등)"""
        try:
            url = f"wss://fstream.binance.com/ws/{self.listen_key}"

            async with websockets.connect(url) as websocket:
                logger.info("사용자 데이터 웹소켓 연결")

                while self.websocket_running:
                    try:
                        message = await asyncio.wait_for(websocket.recv(), timeout=30)
                        data = json.loads(message)

                        # 주문 업데이트 처리
                        if data.get('e') == 'ORDER_TRADE_UPDATE':
                            await self.handle_order_update(data['o'])

                    except asyncio.TimeoutError:
                        # 30초마다 ping 전송
                        await websocket.ping()
                    except Exception as e:
                        logger.error(f"사용자 데이터 웹소켓 오류: {e}")
                        break

        except Exception as e:
            logger.error(f"사용자 데이터 웹소켓 연결 실패: {e}")

    async def handle_order_update(self, order_data):
        """주문 업데이트 처리"""
        try:
            symbol = order_data['s']
            order_status = order_data['X']  # 주문 상태
            order_type = order_data['o']    # 주문 타입
            side = order_data['S']          # 매수/매도
            price = float(order_data['p'])  # 주문 가격
            quantity = float(order_data['q'])  # 주문 수량

            # 지정가 주문이 체결된 경우
            if order_status == 'FILLED' and order_type == 'LIMIT':
                logger.info(f"[{symbol}] 지정가 주문 체결: {side} {quantity} @ {price}")

                # 레이어 체결 기록 (레이어 인덱스는 가격으로 추정)
                if symbol in self.layer_data:
                    for layer_idx, layer_info in self.layer_data[symbol]["layers"].items():
                        layer_price = layer_info["price"]
                        if abs(layer_price - price) < 0.0001:  # 가격 매칭
                            self.update_layer_filled(symbol, int(layer_idx), quantity)
                            break

        except Exception as e:
            logger.error(f"주문 업데이트 처리 실패: {e}")

    def stop_websockets(self):
        """웹소켓 중지"""
        self.websocket_running = False

        if self.price_ws_task:
            self.price_ws_task.cancel()
        if self.user_ws_task:
            self.user_ws_task.cancel()

        # Listen Key 삭제
        if self.listen_key:
            try:
                self.client.futures_stream_close(self.listen_key)
            except:
                pass

    def load_layer_data(self):
        """레이어 추적 데이터 로드"""
        try:
            with open(self.layer_file, 'r', encoding='utf-8') as f:
                self.layer_data = json.load(f)
            logger.info(f"레이어 추적 데이터 로드 완료: {len(self.layer_data)}개 코인")
        except FileNotFoundError:
            self.layer_data = {}
            logger.info("레이어 추적 파일이 없어 새로 생성합니다")
        except Exception as e:
            logger.error(f"레이어 추적 데이터 로드 실패: {e}")
            self.layer_data = {}

    def save_layer_data(self):
        """레이어 추적 데이터 저장"""
        try:
            with open(self.layer_file, 'w', encoding='utf-8') as f:
                json.dump(self.layer_data, f, ensure_ascii=False, indent=2)
        except Exception as e:
            logger.error(f"레이어 추적 데이터 저장 실패: {e}")

    def init_layer_tracking(self, symbol, direction, layer_count, layer_prices):
        """레이어 추적 초기화"""
        self.layer_data[symbol] = {
            "direction": direction,
            "layer_count": layer_count,
            "entry_time": datetime.now().isoformat(),
            "layers": {},
            "partial_sales": {},  # 부분매도 기록
            "realized_pnl": 0.0,  # 실현손익 (부분매도로 인한)
            "total_realized_loss": 0.0,  # 총 실현손해
            "total_realized_profit": 0.0  # 총 실현수익
        }

        # 각 레이어 초기화
        for i, price in enumerate(layer_prices):
            self.layer_data[symbol]["layers"][str(i)] = {
                "price": price,
                "quantity": 0,
                "filled": False,
                "partial_sold": False,
                "partial_sold_quantity": 0
            }

        self.save_layer_data()
        logger.info(f"[{symbol}] 레이어 추적 초기화 완료")

    def update_layer_filled(self, symbol, layer_index, quantity):
        """레이어 체결 업데이트"""
        if symbol in self.layer_data:
            layer_key = str(layer_index)
            if layer_key in self.layer_data[symbol]["layers"]:
                self.layer_data[symbol]["layers"][layer_key]["filled"] = True
                self.layer_data[symbol]["layers"][layer_key]["quantity"] = quantity
                self.save_layer_data()
                logger.info(f"[{symbol}] 레이어 {layer_index} 체결 기록: {quantity}")

    def record_partial_sale(self, symbol, price, quantity, pnl):
        """부분매도 기록"""
        if symbol in self.layer_data:
            price_key = str(price)
            if price_key not in self.layer_data[symbol]["partial_sales"]:
                self.layer_data[symbol]["partial_sales"][price_key] = []

            self.layer_data[symbol]["partial_sales"][price_key].append({
                "quantity": quantity,
                "pnl": pnl,
                "time": datetime.now().isoformat()
            })

            # 실현손익 업데이트
            if pnl > 0:
                self.layer_data[symbol]["total_realized_profit"] += pnl
            else:
                self.layer_data[symbol]["total_realized_loss"] += abs(pnl)

            self.layer_data[symbol]["realized_pnl"] += pnl

            self.save_layer_data()
            logger.info(f"[{symbol}] 부분매도 기록: {price} x {quantity}, PnL: {pnl}")

    def is_partial_sold_at_price(self, symbol, price):
        """해당 가격에서 부분매도 했는지 확인"""
        if symbol not in self.layer_data:
            return False

        price_key = str(price)
        return price_key in self.layer_data[symbol].get("partial_sales", {})

    def clear_layer_tracking(self, symbol):
        """레이어 추적 데이터 삭제"""
        if symbol in self.layer_data:
            del self.layer_data[symbol]
            self.save_layer_data()
            logger.info(f"[{symbol}] 레이어 추적 데이터 삭제")
    
    def get_current_price(self, symbol):
        """현재가 조회 (웹소켓 우선, 없으면 API)"""
        try:
            # 웹소켓에서 가격 조회
            if symbol in self.prices:
                return self.prices[symbol]

            # 웹소켓에 없으면 API로 조회 (초기화 시에만)
            ticker = self.client.futures_symbol_ticker(symbol=symbol)
            price = float(ticker['price'])
            self.prices[symbol] = price  # 캐시에 저장
            return price

        except Exception as e:
            logger.error(f"현재가 조회 실패 ({symbol}): {e}")
            return None
    
    def get_position_info(self, symbol):
        """포지션 정보 조회"""
        try:
            positions = self.client.futures_position_information(symbol=symbol)
            for pos in positions:
                position_amt = float(pos.get('positionAmt', 0))
                if abs(position_amt) > 0.0001:
                    return {
                        'symbol': symbol,
                        'positionAmt': position_amt,
                        'entryPrice': float(pos.get('entryPrice', 0)),
                        'markPrice': float(pos.get('markPrice', 0)),
                        'unrealizedPnl': float(pos.get('unrealizedPnl', 0))
                    }
            return None
        except Exception as e:
            logger.error(f"포지션 정보 조회 실패 ({symbol}): {e}")
            return None
    
    def get_open_orders(self, symbol):
        """활성 주문 조회 (필요시에만 API 호출)"""
        try:
            # 레이어 추적 데이터가 있으면 그것을 기반으로 추정
            if symbol in self.layer_data:
                # 실제 API 호출은 최소화하고, 레이어 데이터 기반으로 추정
                pass

            orders = self.client.futures_get_open_orders(symbol=symbol)
            return orders
        except Exception as e:
            logger.error(f"주문 조회 실패 ({symbol}): {e}")
            return []
    
    def format_quantity(self, quantity, symbol):
        """수량 포맷팅"""
        try:
            info = self.client.futures_exchange_info()
            for s in info['symbols']:
                if s['symbol'] == symbol:
                    for f in s['filters']:
                        if f['filterType'] == 'LOT_SIZE':
                            step_size = float(f['stepSize'])
                            precision = len(f['stepSize'].rstrip('0').split('.')[-1])
                            return round(quantity - (quantity % step_size), precision)
            return round(quantity, 4)
        except:
            return round(quantity, 4)
    
    def format_price(self, price, symbol):
        """가격 포맷팅"""
        try:
            info = self.client.futures_exchange_info()
            for s in info['symbols']:
                if s['symbol'] == symbol:
                    for f in s['filters']:
                        if f['filterType'] == 'PRICE_FILTER':
                            tick_size = float(f['tickSize'])
                            precision = len(f['tickSize'].rstrip('0').split('.')[-1])
                            return round(price - (price % tick_size), precision)
            return round(price, 5)
        except:
            return round(price, 5)
    
    def calculate_rsi_stoch(self, symbol):
        """RSI와 Stochastic RSI 계산"""
        try:
            # 4시간 캔들 데이터 조회
            klines = self.client.futures_klines(symbol=symbol, interval='4h', limit=100)
            closes = [float(k[4]) for k in klines]
            
            if len(closes) < 14:
                return None, None, None
            
            # RSI 계산
            deltas = [closes[i] - closes[i-1] for i in range(1, len(closes))]
            gains = [d if d > 0 else 0 for d in deltas]
            losses = [-d if d < 0 else 0 for d in deltas]
            
            avg_gain = sum(gains[-14:]) / 14
            avg_loss = sum(losses[-14:]) / 14
            
            if avg_loss == 0:
                rsi = 100
            else:
                rs = avg_gain / avg_loss
                rsi = 100 - (100 / (1 + rs))
            
            # Stochastic RSI 계산 (간단 버전)
            rsi_values = []
            for i in range(14, len(closes)):
                period_deltas = [closes[j] - closes[j-1] for j in range(i-13, i+1)]
                period_gains = [d if d > 0 else 0 for d in period_deltas]
                period_losses = [-d if d < 0 else 0 for d in period_deltas]
                
                period_avg_gain = sum(period_gains) / 14
                period_avg_loss = sum(period_losses) / 14
                
                if period_avg_loss == 0:
                    period_rsi = 100
                else:
                    period_rs = period_avg_gain / period_avg_loss
                    period_rsi = 100 - (100 / (1 + period_rs))
                
                rsi_values.append(period_rsi)
            
            if len(rsi_values) < 14:
                return rsi, None, None
            
            recent_rsi = rsi_values[-14:]
            min_rsi = min(recent_rsi)
            max_rsi = max(recent_rsi)
            
            if max_rsi == min_rsi:
                stoch_k = 50
            else:
                stoch_k = (rsi - min_rsi) / (max_rsi - min_rsi) * 100
            
            # %D는 %K의 3일 이동평균 (간단화)
            if len(rsi_values) >= 3:
                recent_k_values = []
                for i in range(len(rsi_values)-2, len(rsi_values)+1):
                    if i < len(rsi_values):
                        period_rsi_for_k = rsi_values[i]
                        period_min = min(rsi_values[max(0, i-13):i+1])
                        period_max = max(rsi_values[max(0, i-13):i+1])
                        if period_max == period_min:
                            k_val = 50
                        else:
                            k_val = (period_rsi_for_k - period_min) / (period_max - period_min) * 100
                        recent_k_values.append(k_val)
                
                stoch_d = sum(recent_k_values) / len(recent_k_values) if recent_k_values else stoch_k
            else:
                stoch_d = stoch_k
            
            return rsi, stoch_k, stoch_d
            
        except Exception as e:
            logger.error(f"지표 계산 실패 ({symbol}): {e}")
            return None, None, None
    
    def check_entry_signal(self, symbol, mode):
        """진입 신호 확인 (포지션이 없을 때만 지표 계산)"""
        try:
            # 포지션이 있으면 지표 계산 안함 (API 호출 최소화)
            position = self.get_position_info(symbol)
            if position:
                return "wait"

            rsi, stoch_k, stoch_d = self.calculate_rsi_stoch(symbol)

            if rsi is None or stoch_k is None or stoch_d is None:
                return "wait"

            logger.info(f"[{symbol}] RSI: {rsi:.1f}, K: {stoch_k:.1f}, D: {stoch_d:.1f}")

            if mode == "conditional":
                # 조건부 진입: 지표에 따라 롱/숏 결정
                if stoch_k > stoch_d and rsi > 20:
                    logger.info(f"[{symbol}] → long")
                    return "long"
                elif stoch_k < stoch_d and rsi < 80:
                    logger.info(f"[{symbol}] → short")
                    return "short"
                else:
                    logger.info(f"[{symbol}] → wait")
                    return "wait"
            elif mode == "long":
                # 롱만: 항상 롱 진입
                return "long"
            elif mode == "short":
                # 숏만: 항상 숏 진입
                return "short"
            else:
                return "wait"

        except Exception as e:
            logger.error(f"진입 신호 확인 실패 ({symbol}): {e}")
            return "wait"

    def execute_cycle_entry(self, symbol, coin_config, entry_direction):
        """순환매매 진입 실행"""
        try:
            logger.info(f"[{symbol}] 순환매매 진입 시작: {entry_direction}")

            # 설정값 로드
            layer_count = coin_config.get('layer_count', 5)
            bottom_pct = coin_config.get('bottom_pct', 50.0)
            fraction = coin_config.get('fraction', 0.1)
            leverage = coin_config.get('leverage', 1)

            # 현재가 조회
            current_price = self.get_current_price(symbol)
            if not current_price:
                logger.error(f"[{symbol}] 현재가 조회 실패")
                return False

            # 잔고 조회
            account = self.client.futures_account()
            usdt_balance = 0
            for asset in account['assets']:
                if asset['asset'] == 'USDT':
                    usdt_balance = float(asset['walletBalance'])
                    break

            if usdt_balance < 10:
                logger.error(f"[{symbol}] 잔고 부족: {usdt_balance} USDT")
                return False

            # 투자 금액 계산
            invest_amount = usdt_balance * fraction

            # 레버리지 설정
            try:
                self.client.futures_change_leverage(symbol=symbol, leverage=leverage)
            except:
                pass  # 이미 설정된 경우 무시

            # 가격 범위 계산
            if entry_direction == "short":
                # 숏 포지션: 현재가 위쪽으로 레이어 배치
                top_price = current_price * (1 + bottom_pct / 100)
                price_gap = (top_price - current_price) / (layer_count - 1) if layer_count > 1 else 0
                logger.info(f"[{symbol}] 숏 포지션: 현재가={current_price}, 최상단={top_price}")
            else:
                # 롱 포지션: 현재가 아래쪽으로 레이어 배치
                bottom_price = current_price * (1 - bottom_pct / 100)
                price_gap = (current_price - bottom_price) / (layer_count - 1) if layer_count > 1 else 0
                logger.info(f"[{symbol}] 롱 포지션: 현재가={current_price}, 최하단={bottom_price}")

            # 모든 레이어 동일 수량 계산
            layer_quantity = invest_amount / current_price / layer_count

            success_count = 0
            layer_prices = []  # 레이어 가격 저장

            # 각 레이어별 주문 실행
            for i in range(layer_count):
                try:
                    # 레이어 가격 계산
                    if entry_direction == "short":
                        layer_price = current_price + (price_gap * i)
                    else:
                        layer_price = current_price - (price_gap * i)

                    # 포맷팅
                    formatted_price = self.format_price(layer_price, symbol)
                    formatted_qty = self.format_quantity(layer_quantity, symbol)

                    # 레이어 가격 저장
                    layer_prices.append(layer_price)

                    if i == 0:
                        # 첫 번째 레이어: 시장가 주문
                        side = "SELL" if entry_direction == "short" else "BUY"

                        order = self.client.futures_create_order(
                            symbol=symbol,
                            side=side,
                            type="MARKET",
                            quantity=formatted_qty
                        )

                        action = "매도" if entry_direction == "short" else "매수"
                        logger.info(f"[{symbol}] 시장가 {action}: {formatted_qty}")
                        success_count += 1

                    else:
                        # 나머지 레이어: 지정가 주문
                        side = "SELL" if entry_direction == "short" else "BUY"

                        order = self.client.futures_create_order(
                            symbol=symbol,
                            side=side,
                            type="LIMIT",
                            quantity=formatted_qty,
                            price=formatted_price,
                            timeInForce="GTC"
                        )

                        action = "매도" if entry_direction == "short" else "매수"
                        logger.info(f"[{symbol}] 지정가 {action}: {formatted_price} x {formatted_qty}")
                        success_count += 1

                except Exception as e:
                    logger.error(f"[{symbol}] 레이어 {i} 주문 실패: {e}")
                    continue

            if success_count > 0:
                logger.info(f"[{symbol}] 순환매매 진입 완료: {success_count}/{layer_count}개 레이어")

                # 레이어 추적 초기화
                self.init_layer_tracking(symbol, entry_direction, layer_count, layer_prices)

                # 첫 번째 레이어(시장가) 체결 기록
                if success_count > 0:
                    self.update_layer_filled(symbol, 0, formatted_qty)

                # 주문 상태 저장
                self.orders[symbol] = {
                    'direction': entry_direction,
                    'layer_count': layer_count,
                    'entry_time': datetime.now(),
                    'success_layers': success_count
                }
                return True
            else:
                logger.error(f"[{symbol}] 순환매매 진입 실패: 모든 레이어 실패")
                return False

        except Exception as e:
            logger.error(f"[{symbol}] 순환매매 진입 오류: {e}")
            return False

    def check_partial_profit(self, symbol, coin_config):
        """부분 익절 확인 및 실행"""
        try:
            if symbol not in self.orders:
                return

            order_info = self.orders[symbol]
            direction = order_info['direction']

            # 현재가 조회
            current_price = self.get_current_price(symbol)
            if not current_price:
                return

            # 포지션 정보 조회
            position = self.get_position_info(symbol)
            if not position:
                return

            # 활성 주문 조회
            open_orders = self.get_open_orders(symbol)
            limit_orders = [o for o in open_orders if o['type'] == 'LIMIT']

            if not limit_orders:
                return

            # 부분 익절 설정
            partial_tp_pct = coin_config.get('partial_tp_pct', 2.0)

            # 체결된 지정가 주문 확인 및 부분 익절
            for order in limit_orders:
                order_price = float(order['price'])
                order_qty = float(order['origQty'])

                # 트리거 가격 계산
                if direction == "short":
                    trigger_price = order_price * (1 - partial_tp_pct / 100)
                    trigger_condition = current_price <= trigger_price
                else:
                    trigger_price = order_price * (1 + partial_tp_pct / 100)
                    trigger_condition = current_price >= trigger_price

                if trigger_condition:
                    # 이미 이 가격에서 부분매도 했는지 확인
                    if self.is_partial_sold_at_price(symbol, order_price):
                        continue

                    try:
                        # 부분 익절 실행
                        close_side = "BUY" if direction == "short" else "SELL"
                        partial_qty = order_qty * 0.5  # 50% 부분 익절
                        formatted_qty = self.format_quantity(partial_qty, symbol)

                        self.client.futures_create_order(
                            symbol=symbol,
                            side=close_side,
                            type="MARKET",
                            quantity=formatted_qty
                        )

                        # 손익 계산
                        if direction == "short":
                            pnl = (order_price - current_price) * partial_qty
                        else:
                            pnl = (current_price - order_price) * partial_qty

                        # 부분매도 기록
                        self.record_partial_sale(symbol, order_price, partial_qty, pnl)

                        logger.info(f"[{symbol}] 부분 익절: {formatted_qty} @ {current_price}, PnL: {pnl:.2f}")

                    except Exception as e:
                        logger.error(f"[{symbol}] 부분 익절 실패: {e}")

        except Exception as e:
            logger.error(f"[{symbol}] 부분 익절 확인 실패: {e}")

    def check_final_profit(self, symbol, coin_config):
        """최종 익절 확인 및 실행"""
        try:
            if symbol not in self.orders:
                return

            order_info = self.orders[symbol]
            direction = order_info['direction']

            # 포지션 정보 조회
            position = self.get_position_info(symbol)
            if not position:
                return

            position_amt = position['positionAmt']
            entry_price = position['entryPrice']
            current_price = self.get_current_price(symbol)

            if not current_price or entry_price == 0:
                return

            # 최종 익절 설정
            final_tp_pct = coin_config.get('final_tp_pct', 5.0)

            # 수익률 계산
            if direction == "short":
                profit_pct = (entry_price - current_price) / entry_price * 100
            else:
                profit_pct = (current_price - entry_price) / entry_price * 100

            if profit_pct >= final_tp_pct:
                try:
                    # 전체 포지션 청산
                    close_side = "BUY" if position_amt < 0 else "SELL"
                    close_qty = abs(position_amt)
                    formatted_qty = self.format_quantity(close_qty, symbol)

                    self.client.futures_create_order(
                        symbol=symbol,
                        side=close_side,
                        type="MARKET",
                        quantity=formatted_qty
                    )

                    # 모든 활성 주문 취소
                    self.client.futures_cancel_all_open_orders(symbol=symbol)

                    logger.info(f"[{symbol}] 최종 익절: {profit_pct:.2f}% 수익")

                    # 레이어 추적 데이터 삭제
                    self.clear_layer_tracking(symbol)

                    # 주문 정보 삭제
                    if symbol in self.orders:
                        del self.orders[symbol]

                except Exception as e:
                    logger.error(f"[{symbol}] 최종 익절 실패: {e}")

        except Exception as e:
            logger.error(f"[{symbol}] 최종 익절 확인 실패: {e}")

    def check_stop_loss(self, symbol, coin_config):
        """손절 확인 및 실행"""
        try:
            if symbol not in self.layer_data:
                return

            # 포지션 정보 조회
            position = self.get_position_info(symbol)
            if not position:
                return

            position_amt = position['positionAmt']
            unrealized_pnl = position['unrealizedPnl']

            # 활성 주문 조회 (남아있는 지정가 주문 물량)
            open_orders = self.get_open_orders(symbol)
            remaining_qty = sum(float(o['origQty']) for o in open_orders if o['type'] == 'LIMIT')

            # 총 물량 (현재 포지션 + 남아있는 지정가 주문)
            total_qty = abs(position_amt) + remaining_qty

            # 레이어 추적 데이터에서 실현손익 가져오기
            layer_info = self.layer_data[symbol]
            total_realized_loss = layer_info.get("total_realized_loss", 0)
            total_realized_profit = layer_info.get("total_realized_profit", 0)

            # 총 손익 = 미실현손익 + 실현손해 - 실현수익
            total_pnl = unrealized_pnl + total_realized_loss - total_realized_profit

            # 손절 기준 계산
            stop_loss_pct = coin_config.get('stop_loss_pct', 10.0)
            current_price = self.get_current_price(symbol)

            if not current_price:
                return

            # 손절 기준 금액 (총 물량 * 현재가 * 손절 퍼센트)
            stop_loss_amount = total_qty * current_price * (stop_loss_pct / 100)

            logger.info(f"[{symbol}] 손익 체크: 총손익={total_pnl:.2f}, 손절기준={-stop_loss_amount:.2f}")

            if total_pnl <= -stop_loss_amount:
                try:
                    # 전체 포지션 청산
                    close_side = "BUY" if position_amt < 0 else "SELL"
                    close_qty = abs(position_amt)
                    formatted_qty = self.format_quantity(close_qty, symbol)

                    if close_qty > 0:
                        self.client.futures_create_order(
                            symbol=symbol,
                            side=close_side,
                            type="MARKET",
                            quantity=formatted_qty
                        )

                    # 모든 활성 주문 취소
                    self.client.futures_cancel_all_open_orders(symbol=symbol)

                    logger.info(f"[{symbol}] 손절 실행: 총손익={total_pnl:.2f}, 손절률={stop_loss_pct}%")

                    # 레이어 추적 데이터 삭제
                    self.clear_layer_tracking(symbol)

                    # 주문 정보 삭제
                    if symbol in self.orders:
                        del self.orders[symbol]

                except Exception as e:
                    logger.error(f"[{symbol}] 손절 실행 실패: {e}")

        except Exception as e:
            logger.error(f"[{symbol}] 손절 확인 실패: {e}")

    async def run_strategy(self):
        """전략 실행 메인 루프"""
        logger.info("순환매매 전략 시작")
        self.running = True

        if not COINS_CONFIG:
            logger.error("설정된 코인이 없습니다")
            return

        logger.info(f"모니터링 코인: {[coin['symbol'] for coin in COINS_CONFIG]}")

        # 웹소켓 시작
        await self.start_websockets()

        # 웹소켓 연결 대기
        await asyncio.sleep(2)

        while self.running:
            try:
                for coin in COINS_CONFIG:
                    symbol = coin['symbol']
                    mode = coin.get('mode', 'conditional')

                    # 기존 포지션 확인
                    position = self.get_position_info(symbol)

                    if position:
                        # 포지션이 있으면 손절, 부분익절, 최종익절 확인
                        self.check_stop_loss(symbol, coin)
                        self.check_partial_profit(symbol, coin)
                        self.check_final_profit(symbol, coin)
                    else:
                        # 포지션이 없으면 진입 신호 확인
                        signal = self.check_entry_signal(symbol, mode)

                        if signal in ["long", "short"]:
                            success = self.execute_cycle_entry(symbol, coin, signal)
                            if success:
                                logger.info(f"[{symbol}] 순환매매 진입 성공: {signal}")

                # 1초 대기
                await asyncio.sleep(1)

            except KeyboardInterrupt:
                logger.info("사용자에 의해 중단됨")
                break
            except Exception as e:
                logger.error(f"전략 실행 오류: {e}")
                await asyncio.sleep(5)

        logger.info("순환매매 전략 종료")

        # 웹소켓 중지
        self.stop_websockets()

    def stop(self):
        """전략 중지"""
        self.running = False
        self.stop_websockets()

def signal_handler(signum, frame):
    """시그널 핸들러"""
    print("\n프로그램을 종료합니다...")
    sys.exit(0)

# 메인 실행
async def main():
    """메인 함수"""
    # 시그널 핸들러 등록
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)

    print("=" * 60)
    print("           단일 사용자용 순환매매 시스템")
    print("=" * 60)
    print()
    print("📋 설정 정보:")
    print(f"   - API 키: {API_KEY[:8]}..." if API_KEY != "YOUR_BINANCE_API_KEY_HERE" else "   - API 키: 설정되지 않음")
    print(f"   - 모니터링 코인: {[coin['symbol'] for coin in COINS_CONFIG]}")
    print()

    # 순환매매 인스턴스 생성
    trader = SimpleCycleTrading()

    if not trader.client:
        print("❌ 바이낸스 API 연결 실패")
        print("파일 상단의 API_KEY와 SECRET_KEY를 확인해주세요.")
        print()
        print("설정 방법:")
        print('API_KEY = "여기에_실제_API_키_입력"')
        print('SECRET_KEY = "여기에_실제_시크릿_키_입력"')
        return

    print("✅ 바이낸스 API 연결 성공")
    print()
    print("전략을 시작합니다... (Ctrl+C로 종료)")
    print("-" * 60)

    try:
        await trader.run_strategy()
    except KeyboardInterrupt:
        print("\n사용자에 의해 종료됨")
    except Exception as e:
        print(f"\n오류 발생: {e}")
        logger.error(f"메인 루프 오류: {e}")
    finally:
        trader.stop()
        print("프로그램이 종료되었습니다.")

if __name__ == "__main__":
    asyncio.run(main())
